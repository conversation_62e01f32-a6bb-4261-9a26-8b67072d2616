#!/bin/bash

# solucion-alternativa-rustdesk.sh
# Solución alternativa para problemas de "Connection reset by peer"
# Ejecutar en el SERVIDOR (************)

echo "=== SOLUCIÓN ALTERNATIVA RUSTDESK ==="
echo "Esta solución configura RustDesk para usar el método de ID"
echo "en lugar de conexión directa IP"
echo ""
echo "Ejecutar en el SERVIDOR (************)"
echo "Presiona Enter para continuar..."
read

# Colores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[OK]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARN]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. Detener completamente RustDesk
print_status "Deteniendo RustDesk completamente..."
sudo systemctl stop rustdesk 2>/dev/null || true
killall rustdesk 2>/dev/null || true
killall -9 rustdesk 2>/dev/null || true
sleep 3

# 2. Limpiar configuración
print_status "Limpiando configuración anterior..."
if [ -d ~/.config/rustdesk ]; then
    mv ~/.config/rustdesk ~/.config/rustdesk.backup.$(date +%Y%m%d_%H%M%S)
    print_status "Backup creado de configuración anterior"
fi

mkdir -p ~/.config/rustdesk

# 3. Crear configuración simplificada
print_status "Creando configuración simplificada..."
cat > ~/.config/rustdesk/RustDesk2.toml << 'EOF'
# Configuración RustDesk SERVIDOR - Método ID
# Configuración simplificada para evitar problemas de protocolo

[options]
# === CONFIGURACIÓN BÁSICA ===
# Usar servidores públicos de RustDesk para relay
# Esto evita problemas de protocolo directo
custom-rendezvous-server = ""
relay-server = ""
api-server = ""

# === CONFIGURACIÓN DE ACCESO ===
enable-password = true
enable-permanent-password = true
enable-unattended-access = true
enable-remote-access = true

# === CONFIGURACIÓN DE RED SIMPLIFICADA ===
enable-direct-ip-access = false
enable-lan-discovery = true
enable-tunnel = true
enable-tcp = true
enable-udp = true

# === CONFIGURACIÓN DE SEGURIDAD ===
enable-2fa = false
enable-keyboard = true
enable-clipboard = true
enable-file-transfer = true
enable-audio = true

# === CONFIGURACIÓN DE RENDIMIENTO ===
enable-hardware-codec = false
enable-gpu = false
video-codec = "VP8"
audio-codec = "Opus"
image-quality = "Balanced"

# === LOGGING ===
log-level = "info"
enable-file-logging = true

# === CONFIGURACIÓN SIMPLIFICADA ===
enable-upnp = true
enable-local-relay = false
enable-direct-relay = false
EOF

print_success "Configuración simplificada aplicada"

# 4. Configurar contraseña
print_status "Configurando contraseña..."
echo ""
echo "Introduce una contraseña para RustDesk (mínimo 6 caracteres):"
read -s PASSWORD
echo ""

if [ ${#PASSWORD} -lt 6 ]; then
    print_error "La contraseña debe tener al menos 6 caracteres"
    exit 1
fi

echo "$PASSWORD" > ~/.config/rustdesk/password
print_success "Contraseña configurada"

# 5. Iniciar RustDesk en modo GUI
print_status "Iniciando RustDesk en modo GUI..."

# Asegurar que tenemos display
if [ -z "$DISPLAY" ]; then
    export DISPLAY=:0
fi

# Iniciar RustDesk en modo GUI (no servicio)
rustdesk &
RUSTDESK_PID=$!
sleep 5

# Verificar que esté ejecutándose
if ps -p $RUSTDESK_PID > /dev/null; then
    print_success "RustDesk iniciado correctamente (PID: $RUSTDESK_PID)"
else
    print_warning "Reintentando inicio de RustDesk..."
    DISPLAY=:0 rustdesk &
    sleep 3
fi

# 6. Obtener ID de RustDesk
print_status "Obteniendo ID de RustDesk..."
sleep 2

# Intentar obtener ID de diferentes maneras
RUSTDESK_ID=""

# Método 1: Comando directo
RUSTDESK_ID=$(rustdesk --get-id 2>/dev/null || echo "")

# Método 2: Desde archivo de configuración
if [ -z "$RUSTDESK_ID" ] && [ -f ~/.config/rustdesk/RustDesk2.toml ]; then
    RUSTDESK_ID=$(grep -E "^id\s*=" ~/.config/rustdesk/RustDesk2.toml 2>/dev/null | cut -d'=' -f2 | tr -d ' "' || echo "")
fi

# Método 3: Desde logs
if [ -z "$RUSTDESK_ID" ] && [ -d ~/.config/rustdesk/log ]; then
    RUSTDESK_ID=$(grep -r "ID:" ~/.config/rustdesk/log/ 2>/dev/null | tail -1 | grep -oE '[0-9]{9,}' || echo "")
fi

if [ -n "$RUSTDESK_ID" ]; then
    print_success "ID obtenido: $RUSTDESK_ID"
else
    print_warning "No se pudo obtener el ID automáticamente"
    RUSTDESK_ID="[Verificar en la interfaz de RustDesk]"
fi

# 7. Información de conexión
TAILSCALE_IP=$(tailscale ip -4 2>/dev/null || echo "************")

echo ""
echo "=== INFORMACIÓN DE CONEXIÓN ==="
echo "• Método: Conexión por ID (recomendado)"
echo "• ID RustDesk: $RUSTDESK_ID"
echo "• IP Tailscale: $TAILSCALE_IP"
echo "• Contraseña: [configurada]"
echo ""
echo "=== INSTRUCCIONES PARA EL CLIENTE ==="
echo ""
echo "1. 🎯 MÉTODO RECOMENDADO (Por ID):"
echo "   • Abre RustDesk en el cliente"
echo "   • Introduce el ID: $RUSTDESK_ID"
echo "   • Clic 'Conectar'"
echo "   • Introduce la contraseña"
echo ""
echo "2. 🔄 MÉTODO ALTERNATIVO (Si el ID no funciona):"
echo "   • Abre RustDesk en el cliente"
echo "   • Ve a Configuración > Red"
echo "   • Configura servidor personalizado: $TAILSCALE_IP"
echo "   • Reinicia RustDesk y conecta por ID"
echo ""
echo "3. 📱 VERIFICAR EN EL SERVIDOR:"
echo "   • Abre RustDesk en el servidor"
echo "   • Verifica que aparezca el ID en la pantalla principal"
echo "   • Anota el ID si es diferente al mostrado arriba"
echo ""

print_success "Configuración alternativa completada"
echo ""
print_warning "IMPORTANTE:"
echo "• Este método usa los servidores de relay de RustDesk"
echo "• La conexión será a través de Tailscale + relay público"
echo "• Debería resolver el problema de 'Connection reset by peer'"
echo "• Si persisten problemas, verifica que ambos dispositivos"
echo "  tengan la misma versión de RustDesk"
