#!/bin/bash

# 15-configurar-rustdesk-cliente-tailscale.sh
# Script para configurar RustDesk cliente para usar Tailscale
# Ejecutar en el dispositivo desde donde te quieres conectar

echo "=== CONFIGURAR RUSTDESK CLIENTE PARA TAILSCALE ==="
echo "Este script configura RustDesk para conectar directamente"
echo "a través de Tailscale sin usar servidores externos"
echo ""
echo "Ejecuta esto en el dispositivo DESDE DONDE te quieres conectar"
echo "(NO en el servidor Manjaro)"
echo ""
echo "Presiona Enter para continuar o Ctrl+C para cancelar"
read

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Verificar que Tailscale esté funcionando
print_status "Verificando Tailscale..."
if ! command -v tailscale &> /dev/null; then
    print_error "Tailscale no está instalado en este dispositivo"
    echo "Instala Tailscale primero:"
    echo "yay -S tailscale"
    echo "sudo systemctl enable --now tailscaled"
    echo "sudo tailscale up"
    exit 1
fi

if ! tailscale status &> /dev/null; then
    print_error "Tailscale no está conectado"
    echo "Ejecuta: sudo tailscale up"
    exit 1
fi

print_success "Tailscale funcionando correctamente"

# 2. Detener RustDesk si está ejecutándose
print_status "Deteniendo RustDesk..."
killall rustdesk 2>/dev/null || true

# 3. Configurar RustDesk para conexión directa
print_status "Configurando RustDesk para conexión directa..."
mkdir -p ~/.config/rustdesk

cat > ~/.config/rustdesk/RustDesk2.toml << 'EOF'
# Configuración RustDesk CLIENTE para Tailscale
# Conexión directa sin servidores externos

[options]
# === CONFIGURACIÓN DE RED ===
# NO usar servidores externos - conexión directa
custom-rendezvous-server = ""
relay-server = ""
api-server = ""

# Habilitar conexión directa
enable-direct-ip-access = true
enable-lan-discovery = true
enable-tunnel = false
enable-tcp = true
enable-udp = true

# === OPTIMIZACIONES DE RENDIMIENTO ===
enable-hardware-codec = true
enable-gpu = true
video-codec = "VP9"
audio-codec = "Opus"
image-quality = "Balanced"

# === CONFIGURACIÓN DE SEGURIDAD ===
enable-password = true
enable-2fa = false
enable-keyboard = true
enable-clipboard = true
enable-file-transfer = true
enable-audio = true

# === CONFIGURACIÓN DE INTERFAZ ===
theme = "dark"
enable-confirm-closing-tabs = true
enable-open-new-connections-in-tabs = true

# === LOGGING ===
log-level = "info"
enable-file-logging = true

# === CONFIGURACIONES PARA CONEXIÓN DIRECTA ===
enable-upnp = false
enable-local-relay = true
enable-direct-relay = true
enable-tcp-tunneling = false
EOF

print_success "Configuración aplicada"

# 4. Mostrar información de conexión
print_status "Obteniendo información de red Tailscale..."

# Obtener dispositivos en la red Tailscale
echo ""
echo "=== DISPOSITIVOS EN TU RED TAILSCALE ==="
tailscale status | while read line; do
    if [[ $line =~ ^[0-9] ]]; then
        IP=$(echo $line | awk '{print $1}')
        NAME=$(echo $line | awk '{print $2}')
        echo "  ✓ $NAME: $IP"
    fi
done

echo ""
echo "=== CÓMO CONECTAR ==="
echo ""
echo "1. 🎯 MÉTODO DIRECTO (Recomendado):"
echo "   • Abre RustDesk"
echo "   • En 'ID del Socio' introduce: ************:21115"
echo "   • Clic 'Conectar'"
echo "   • Introduce la contraseña"
echo ""
echo "2. 🆔 MÉTODO POR ID (Alternativo):"
echo "   • Abre RustDesk"
echo "   • Introduce el ID normal de RustDesk"
echo "   • Si falla, usa el método directo"
echo ""
echo "=== INFORMACIÓN IMPORTANTE ==="
echo "• IP del servidor Manjaro: ************"
echo "• Puerto: 21115"
echo "• Conexión: Directa a través de Tailscale"
echo "• No usa servidores externos"

print_success "Configuración completada"
echo ""
print_warning "IMPORTANTE: Ahora abre RustDesk y prueba conectar con:"
echo "************:21115"
