#!/bin/bash

# diagnostico-avanzado-rustdesk.sh
# Diagnóstico avanzado para problemas de "Connection reset by peer"

echo "=== DIAGNÓSTICO AVANZADO RUSTDESK ==="
echo "Ejecutar en el SERVIDOR (100.72.95.41)"
echo ""

# Colores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[OK]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARN]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. Verificar versión de RustDesk
print_status "Verificando versión de RustDesk..."
RUSTDESK_VERSION=$(rustdesk --version 2>/dev/null || echo "No disponible")
echo "Versión: $RUSTDESK_VERSION"

# 2. Verificar configuración actual
print_status "Verificando configuración actual..."
CONFIG_FILE="$HOME/.config/rustdesk/RustDesk2.toml"
if [ -f "$CONFIG_FILE" ]; then
    echo "=== CONFIGURACIÓN ACTUAL ==="
    cat "$CONFIG_FILE"
    echo "=========================="
else
    print_error "No se encontró archivo de configuración"
fi

# 3. Verificar logs detallados
print_status "Verificando logs de RustDesk..."
LOG_DIR="$HOME/.config/rustdesk/log"
if [ -d "$LOG_DIR" ]; then
    echo "=== LOGS RECIENTES ==="
    find "$LOG_DIR" -name "*.log" -mtime -1 -exec echo "=== {} ===" \; -exec tail -20 {} \;
else
    print_warning "No se encontró directorio de logs"
fi

# 4. Verificar procesos detallados
print_status "Verificando procesos de RustDesk..."
echo "=== PROCESOS RUSTDESK ==="
ps aux | grep rustdesk | grep -v grep
echo ""
echo "=== PUERTOS ABIERTOS ==="
netstat -tlnp | grep rustdesk
echo ""

# 5. Probar protocolo RustDesk
print_status "Probando protocolo RustDesk..."
echo "Enviando handshake de prueba al puerto 21115..."

# Crear un script de prueba de protocolo
cat > /tmp/test_rustdesk_protocol.py << 'EOF'
#!/usr/bin/env python3
import socket
import time

def test_rustdesk_connection():
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        print("Conectando a 127.0.0.1:21115...")
        sock.connect(('127.0.0.1', 21115))
        print("✓ Conexión TCP establecida")
        
        # Enviar un handshake básico de RustDesk
        # RustDesk usa un protocolo binario específico
        handshake = b'\x00\x00\x00\x01'  # Mensaje básico de handshake
        sock.send(handshake)
        print("✓ Handshake enviado")
        
        # Intentar recibir respuesta
        sock.settimeout(2)
        response = sock.recv(1024)
        print(f"✓ Respuesta recibida: {len(response)} bytes")
        print(f"  Datos: {response.hex()}")
        
    except socket.timeout:
        print("✗ Timeout - El servidor no responde al handshake")
    except ConnectionResetError:
        print("✗ Connection reset by peer - El servidor rechaza el protocolo")
    except Exception as e:
        print(f"✗ Error: {e}")
    finally:
        sock.close()

if __name__ == "__main__":
    test_rustdesk_connection()
EOF

if command -v python3 &> /dev/null; then
    python3 /tmp/test_rustdesk_protocol.py
else
    print_warning "Python3 no disponible para prueba de protocolo"
fi

# 6. Verificar configuración de red
print_status "Verificando configuración de red..."
echo "=== INTERFACES DE RED ==="
ip addr show | grep -E "(inet|tailscale)"
echo ""
echo "=== RUTAS ==="
ip route | grep 100.
echo ""

# 7. Sugerencias de solución
echo ""
echo "=== ANÁLISIS Y RECOMENDACIONES ==="
echo ""

# Verificar si RustDesk está ejecutándose correctamente
if pgrep rustdesk > /dev/null; then
    print_success "RustDesk está ejecutándose"
else
    print_error "RustDesk NO está ejecutándose"
    echo "Solución: rustdesk --service"
fi

# Verificar puerto 21115
if netstat -tln | grep :21115 > /dev/null; then
    print_success "Puerto 21115 está abierto"
else
    print_error "Puerto 21115 NO está abierto"
    echo "Problema: RustDesk no está escuchando en el puerto correcto"
fi

echo ""
echo "=== POSIBLES SOLUCIONES ==="
echo "1. Reiniciar RustDesk con configuración limpia"
echo "2. Usar método de conexión por ID en lugar de IP directa"
echo "3. Verificar compatibilidad de versiones"
echo "4. Configurar relay server local"
echo ""

rm -f /tmp/test_rustdesk_protocol.py
