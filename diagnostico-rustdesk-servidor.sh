#!/bin/bash

# diagnostico-rustdesk-servidor.sh
# Script para diagnosticar problemas de RustDesk en el servidor
# Ejecutar en el servidor <PERSON> (************)

echo "=== DIAGNÓSTICO RUSTDESK SERVIDOR ==="
echo "Ejecuta este script en el servidor Man<PERSON> (************)"
echo ""

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Verificar si RustDesk está instalado
print_status "Verificando instalación de RustDesk..."
if ! command -v rustdesk &> /dev/null; then
    print_error "RustDesk no está instalado"
    echo "Instala RustDesk:"
    echo "yay -S rustdesk-bin"
    exit 1
else
    print_success "RustDesk está instalado"
fi

# 2. Verificar procesos de RustDesk
print_status "Verificando procesos de RustDesk..."
RUSTDESK_PROCESSES=$(ps aux | grep rustdesk | grep -v grep)
if [ -z "$RUSTDESK_PROCESSES" ]; then
    print_warning "No hay procesos de RustDesk ejecutándose"
else
    print_success "Procesos de RustDesk encontrados:"
    echo "$RUSTDESK_PROCESSES"
fi

# 3. Verificar puertos abiertos
print_status "Verificando puertos abiertos..."
echo "Puertos relacionados con RustDesk:"
netstat -tlnp | grep -E "(21115|21116|21117|21118|21119)" || echo "No se encontraron puertos RustDesk abiertos"

# 4. Verificar configuración de RustDesk
print_status "Verificando configuración de RustDesk..."
CONFIG_FILE="$HOME/.config/rustdesk/RustDesk2.toml"
if [ -f "$CONFIG_FILE" ]; then
    print_success "Archivo de configuración encontrado: $CONFIG_FILE"
    echo "Contenido relevante:"
    grep -E "(enable-direct|enable-tcp|enable-udp|custom-rendezvous)" "$CONFIG_FILE" 2>/dev/null || echo "No se encontraron configuraciones relevantes"
else
    print_warning "No se encontró archivo de configuración en $CONFIG_FILE"
fi

# 5. Verificar Tailscale
print_status "Verificando Tailscale..."
if command -v tailscale &> /dev/null; then
    if tailscale status &> /dev/null; then
        print_success "Tailscale funcionando"
        echo "Estado de Tailscale:"
        tailscale status | head -5
    else
        print_error "Tailscale no está conectado"
    fi
else
    print_error "Tailscale no está instalado"
fi

# 6. Verificar firewall
print_status "Verificando firewall..."
if command -v ufw &> /dev/null; then
    UFW_STATUS=$(sudo ufw status 2>/dev/null)
    echo "Estado UFW: $UFW_STATUS"
elif command -v firewall-cmd &> /dev/null; then
    echo "Firewalld detectado"
    sudo firewall-cmd --list-ports 2>/dev/null || echo "No se pudo verificar firewalld"
else
    echo "No se detectó firewall UFW o firewalld"
fi

# 7. Verificar logs de RustDesk
print_status "Verificando logs de RustDesk..."
LOG_DIRS=(
    "$HOME/.config/rustdesk/log"
    "$HOME/.local/share/rustdesk/log"
    "/tmp/rustdesk"
)

for LOG_DIR in "${LOG_DIRS[@]}"; do
    if [ -d "$LOG_DIR" ]; then
        print_success "Directorio de logs encontrado: $LOG_DIR"
        echo "Archivos de log recientes:"
        ls -la "$LOG_DIR" | head -5
        echo ""
        echo "Últimas líneas del log más reciente:"
        LATEST_LOG=$(ls -t "$LOG_DIR"/*.log 2>/dev/null | head -1)
        if [ -n "$LATEST_LOG" ]; then
            tail -10 "$LATEST_LOG"
        fi
        break
    fi
done

echo ""
echo "=== RECOMENDACIONES ==="
echo ""
echo "1. Si RustDesk no está ejecutándose:"
echo "   rustdesk --service"
echo ""
echo "2. Si el puerto 21115 no está abierto:"
echo "   Verifica que RustDesk esté configurado para escuchar en ese puerto"
echo ""
echo "3. Para reiniciar RustDesk:"
echo "   killall rustdesk"
echo "   rustdesk --service"
echo ""
echo "4. Para verificar conectividad desde el cliente:"
echo "   telnet ************ 21115"
echo ""
echo "5. Si persisten los problemas, revisa los logs mostrados arriba"
