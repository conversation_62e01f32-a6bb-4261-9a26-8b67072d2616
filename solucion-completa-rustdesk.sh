#!/bin/bash

# solucion-completa-rustdesk.sh
# Solución completa para problemas persistentes de RustDesk
# Ejecutar en el SERVIDOR (************)

echo "=== SOLUCIÓN COMPLETA RUSTDESK ==="
echo "Esta solución reinstala y reconfigura RustDesk completamente"
echo "para resolver problemas de 'Connection reset by peer'"
echo ""
echo "Ejecutar en el SERVIDOR (************)"
echo "Presiona Enter para continuar..."
read

# Colores
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[OK]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARN]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 1. Detener y limpiar completamente RustDesk
print_status "Limpieza completa de RustDesk..."
sudo systemctl stop rustdesk 2>/dev/null || true
sudo systemctl disable rustdesk 2>/dev/null || true
killall rustdesk 2>/dev/null || true
killall -9 rustdesk 2>/dev/null || true
sleep 3

# Limpiar archivos de configuración
rm -rf ~/.config/rustdesk*
rm -rf ~/.local/share/rustdesk*
rm -rf /tmp/rustdesk*
sudo rm -f /etc/systemd/system/rustdesk.service

print_success "Limpieza completa realizada"

# 2. Reinstalar RustDesk
print_status "Reinstalando RustDesk..."
yay -R rustdesk-bin --noconfirm 2>/dev/null || true
yay -S rustdesk-bin --noconfirm

if ! command -v rustdesk &> /dev/null; then
    print_error "Error en la instalación de RustDesk"
    exit 1
fi

print_success "RustDesk reinstalado"

# 3. Verificar versión
RUSTDESK_VERSION=$(rustdesk --version 2>/dev/null || echo "Desconocida")
print_status "Versión de RustDesk: $RUSTDESK_VERSION"

# 4. Configuración inicial básica
print_status "Configuración inicial básica..."
mkdir -p ~/.config/rustdesk

# Configuración mínima para evitar conflictos
cat > ~/.config/rustdesk/RustDesk2.toml << 'EOF'
[options]
# Configuración mínima para máxima compatibilidad
enable-password = true
enable-permanent-password = true
enable-unattended-access = true
enable-keyboard = true
enable-clipboard = true
enable-file-transfer = true
enable-audio = true
log-level = "debug"
enable-file-logging = true
EOF

# 5. Configurar contraseña
print_status "Configurando contraseña..."
echo ""
echo "Introduce una contraseña para RustDesk (mínimo 6 caracteres):"
read -s PASSWORD
echo ""

if [ ${#PASSWORD} -lt 6 ]; then
    print_error "La contraseña debe tener al menos 6 caracteres"
    exit 1
fi

echo "$PASSWORD" > ~/.config/rustdesk/password
print_success "Contraseña configurada"

# 6. Iniciar RustDesk y obtener ID
print_status "Iniciando RustDesk..."

# Asegurar display
export DISPLAY=:0

# Iniciar RustDesk
rustdesk &
RUSTDESK_PID=$!
sleep 10

# Verificar que esté ejecutándose
if ! ps -p $RUSTDESK_PID > /dev/null 2>&1; then
    print_warning "Primer intento falló, reintentando..."
    rustdesk &
    sleep 5
fi

# 7. Obtener ID y configuración
print_status "Obteniendo información de conexión..."

# Esperar a que RustDesk genere su ID
sleep 5

# Intentar obtener ID
RUSTDESK_ID=""
for i in {1..10}; do
    RUSTDESK_ID=$(rustdesk --get-id 2>/dev/null || echo "")
    if [ -n "$RUSTDESK_ID" ] && [ "$RUSTDESK_ID" != "0" ]; then
        break
    fi
    sleep 2
done

TAILSCALE_IP=$(tailscale ip -4 2>/dev/null || echo "************")

echo ""
echo "=== INFORMACIÓN DE CONEXIÓN ==="
echo "• ID RustDesk: $RUSTDESK_ID"
echo "• IP Tailscale: $TAILSCALE_IP"
echo "• Contraseña: [configurada]"
echo "• Versión: $RUSTDESK_VERSION"
echo ""

# 8. Verificar puertos
print_status "Verificando puertos..."
echo "Puertos abiertos por RustDesk:"
netstat -tlnp | grep rustdesk || echo "No se encontraron puertos específicos"

# 9. Crear script de prueba de conectividad
print_status "Creando script de prueba..."
cat > ~/test-rustdesk-connection.sh << 'EOF'
#!/bin/bash
echo "=== PRUEBA DE CONECTIVIDAD RUSTDESK ==="
echo "Ejecutar desde el CLIENTE"
echo ""

SERVIDOR_IP="************"
PUERTOS=(21115 21116 21117 21118 21119)

echo "Probando conectividad a $SERVIDOR_IP..."
ping -c 3 $SERVIDOR_IP

echo ""
echo "Probando puertos RustDesk..."
for puerto in "${PUERTOS[@]}"; do
    echo -n "Puerto $puerto: "
    if timeout 3 bash -c "echo >/dev/tcp/$SERVIDOR_IP/$puerto" 2>/dev/null; then
        echo "✓ Abierto"
    else
        echo "✗ Cerrado/Filtrado"
    fi
done

echo ""
echo "Prueba de telnet al puerto principal:"
echo "telnet $SERVIDOR_IP 21115"
EOF

chmod +x ~/test-rustdesk-connection.sh

# 10. Instrucciones finales
echo ""
echo "=== INSTRUCCIONES PARA CONECTAR ==="
echo ""
echo "1. 📋 COPIA ESTE SCRIPT AL CLIENTE:"
echo "   scp ~/test-rustdesk-connection.sh usuario@cliente:~/"
echo ""
echo "2. 🧪 EJECUTA LA PRUEBA EN EL CLIENTE:"
echo "   ./test-rustdesk-connection.sh"
echo ""
echo "3. 🔗 MÉTODOS DE CONEXIÓN:"
echo ""
echo "   A) Por ID (recomendado):"
echo "      • ID: $RUSTDESK_ID"
echo "      • Contraseña: [la que configuraste]"
echo ""
echo "   B) Por IP directa:"
echo "      • IP: $TAILSCALE_IP:21115"
echo "      • Contraseña: [la que configuraste]"
echo ""
echo "4. 🔍 SI PERSISTEN PROBLEMAS:"
echo "   • Verifica que ambos dispositivos tengan la misma versión"
echo "   • Ejecuta: rustdesk --version (en ambos)"
echo "   • Revisa logs: tail -f ~/.config/rustdesk/log/*.log"
echo ""

# 11. Verificación final
print_status "Verificación final..."
if pgrep rustdesk > /dev/null; then
    print_success "✓ RustDesk está ejecutándose"
else
    print_error "✗ RustDesk no está ejecutándose"
fi

if [ -n "$RUSTDESK_ID" ] && [ "$RUSTDESK_ID" != "0" ]; then
    print_success "✓ ID obtenido: $RUSTDESK_ID"
else
    print_warning "✗ No se pudo obtener el ID automáticamente"
    echo "  Verifica el ID manualmente en la interfaz de RustDesk"
fi

if tailscale status &> /dev/null; then
    print_success "✓ Tailscale funcionando"
else
    print_error "✗ Problema con Tailscale"
fi

print_success "Configuración completa finalizada"
echo ""
print_warning "PRÓXIMOS PASOS:"
echo "1. Copia el script de prueba al cliente"
echo "2. Ejecuta la prueba de conectividad"
echo "3. Intenta conectar usando el ID: $RUSTDESK_ID"
echo "4. Si falla, revisa los logs en ambos dispositivos"
