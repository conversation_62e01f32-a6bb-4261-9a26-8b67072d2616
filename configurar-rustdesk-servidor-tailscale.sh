#!/bin/bash

# configurar-rustdesk-servidor-tailscale.sh
# Script para configurar RustDesk servidor para usar con Tailscale
# Ejecutar en el servidor Manjaro (************)

echo "=== CONFIGURAR RUSTDESK SERVIDOR PARA TAILSCALE ==="
echo "Este script configura RustDesk en el servidor para aceptar"
echo "conexiones directas a través de Tailscale"
echo ""
echo "Ejecuta esto en el servidor Manjaro (************)"
echo ""
echo "Presiona Enter para continuar o Ctrl+C para cancelar"
read

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Verificar que Tailscale esté funcionando
print_status "Verificando Tailscale..."
if ! command -v tailscale &> /dev/null; then
    print_error "Tailscale no está instalado"
    echo "Instala Tailscale:"
    echo "yay -S tailscale"
    echo "sudo systemctl enable --now tailscaled"
    echo "sudo tailscale up"
    exit 1
fi

if ! tailscale status &> /dev/null; then
    print_error "Tailscale no está conectado"
    echo "Ejecuta: sudo tailscale up"
    exit 1
fi

print_success "Tailscale funcionando correctamente"

# 2. Verificar/instalar RustDesk
print_status "Verificando RustDesk..."
if ! command -v rustdesk &> /dev/null; then
    print_warning "RustDesk no está instalado. Instalando..."
    yay -S rustdesk-bin --noconfirm
    if [ $? -ne 0 ]; then
        print_error "Error instalando RustDesk"
        exit 1
    fi
fi

print_success "RustDesk está disponible"

# 3. Detener RustDesk si está ejecutándose
print_status "Deteniendo RustDesk..."
killall rustdesk 2>/dev/null || true
sleep 2

# 4. Detener RustDesk completamente
print_status "Deteniendo RustDesk completamente..."
killall rustdesk 2>/dev/null || true
killall -9 rustdesk 2>/dev/null || true
sleep 3

# 5. Configurar RustDesk para servidor
print_status "Configurando RustDesk para servidor..."
mkdir -p ~/.config/rustdesk

# Backup de configuración existente
if [ -f ~/.config/rustdesk/RustDesk2.toml ]; then
    cp ~/.config/rustdesk/RustDesk2.toml ~/.config/rustdesk/RustDesk2.toml.backup.$(date +%Y%m%d_%H%M%S)
    print_status "Backup de configuración creado"
fi

cat > ~/.config/rustdesk/RustDesk2.toml << 'EOF'
# Configuración RustDesk SERVIDOR para Tailscale
# Acepta conexiones directas sin servidores externos

[options]
# === CONFIGURACIÓN DE RED ===
# NO usar servidores externos - conexión directa
custom-rendezvous-server = ""
relay-server = ""
api-server = ""

# Habilitar conexión directa
enable-direct-ip-access = true
enable-lan-discovery = true
enable-tunnel = false
enable-tcp = true
enable-udp = true

# === CONFIGURACIÓN DE SERVIDOR ===
# Permitir conexiones entrantes
enable-remote-access = true
enable-password = true
enable-permanent-password = true
enable-unattended-access = true

# === OPTIMIZACIONES DE RENDIMIENTO ===
enable-hardware-codec = true
enable-gpu = true
video-codec = "VP9"
audio-codec = "Opus"
image-quality = "Balanced"

# === CONFIGURACIÓN DE SEGURIDAD ===
enable-2fa = false
enable-keyboard = true
enable-clipboard = true
enable-file-transfer = true
enable-audio = true
disable-audio = false
disable-clipboard = false

# === CONFIGURACIÓN DE INTERFAZ ===
theme = "dark"
enable-confirm-closing-tabs = true

# === LOGGING ===
log-level = "debug"
enable-file-logging = true

# === CONFIGURACIONES PARA CONEXIÓN DIRECTA ===
enable-upnp = false
enable-local-relay = true
enable-direct-relay = true
enable-tcp-tunneling = false
direct-server = true

# === CONFIGURACIÓN DE PUERTO ===
# Puerto para conexiones directas
listen-port = 21115
EOF

print_success "Configuración aplicada"

# 5. Configurar contraseña permanente
print_status "Configurando contraseña permanente..."
echo ""
echo "Introduce una contraseña para RustDesk (mínimo 6 caracteres):"
read -s PASSWORD
echo ""

if [ ${#PASSWORD} -lt 6 ]; then
    print_error "La contraseña debe tener al menos 6 caracteres"
    exit 1
fi

# Crear archivo de contraseña
echo "$PASSWORD" > ~/.config/rustdesk/password

print_success "Contraseña configurada"

# 6. Iniciar RustDesk como servicio
print_status "Iniciando RustDesk como servicio..."

# Asegurar que no hay procesos previos
killall rustdesk 2>/dev/null || true
sleep 2

# Iniciar RustDesk en modo servicio con configuración específica
print_status "Iniciando RustDesk con configuración de servidor..."
rustdesk --service --config ~/.config/rustdesk/RustDesk2.toml &
sleep 5

# Verificar que esté ejecutándose
RUSTDESK_PID=$(pgrep rustdesk)
if [ -n "$RUSTDESK_PID" ]; then
    print_success "RustDesk iniciado correctamente (PID: $RUSTDESK_PID)"
else
    print_warning "Primer intento falló. Intentando método alternativo..."
    # Método alternativo: iniciar en modo GUI y luego minimizar
    DISPLAY=:0 rustdesk &
    sleep 3
    RUSTDESK_PID=$(pgrep rustdesk)
    if [ -n "$RUSTDESK_PID" ]; then
        print_success "RustDesk iniciado en modo GUI (PID: $RUSTDESK_PID)"
    else
        print_error "Error iniciando RustDesk. Verifica la instalación."
    fi
fi

# 7. Verificar puerto
print_status "Verificando puerto 21115..."
if netstat -tlnp | grep :21115 > /dev/null; then
    print_success "Puerto 21115 está abierto"
else
    print_warning "Puerto 21115 no está abierto"
    echo "RustDesk puede estar usando un puerto diferente"
fi

# 8. Mostrar información de conexión
print_status "Obteniendo información de conexión..."

# Obtener IP de Tailscale
TAILSCALE_IP=$(tailscale ip -4)
RUSTDESK_ID=$(rustdesk --get-id 2>/dev/null || echo "No disponible")

echo ""
echo "=== INFORMACIÓN DE CONEXIÓN ==="
echo "• IP Tailscale: $TAILSCALE_IP"
echo "• Puerto: 21115"
echo "• ID RustDesk: $RUSTDESK_ID"
echo "• Contraseña: [configurada]"
echo ""
echo "=== PARA CONECTAR DESDE EL CLIENTE ==="
echo "Usa la dirección: $TAILSCALE_IP:21115"
echo "O el ID: $RUSTDESK_ID"
echo ""

# 9. Crear servicio systemd (opcional)
print_status "¿Quieres crear un servicio systemd para RustDesk? (y/n)"
read -n 1 CREATE_SERVICE
echo ""

if [[ $CREATE_SERVICE =~ ^[Yy]$ ]]; then
    print_status "Creando servicio systemd..."
    
    sudo tee /etc/systemd/system/rustdesk.service > /dev/null << EOF
[Unit]
Description=RustDesk Remote Desktop
After=network.target

[Service]
Type=simple
User=$USER
ExecStart=/usr/bin/rustdesk --service
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

    sudo systemctl daemon-reload
    sudo systemctl enable rustdesk
    sudo systemctl start rustdesk
    
    print_success "Servicio systemd creado y habilitado"
fi

print_success "Configuración del servidor completada"
echo ""
print_warning "IMPORTANTE:"
echo "• El servidor está listo para recibir conexiones"
echo "• Usa la IP: $TAILSCALE_IP:21115"
echo "• O el ID: $RUSTDESK_ID"
echo "• Asegúrate de que el cliente esté configurado correctamente"
